@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@400;500;600;700;800;900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 text-gray-900 font-sans;
    background-attachment: fixed;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-display font-semibold;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gradient-to-b from-primary-400 to-primary-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply from-primary-500 to-primary-700;
  }
}

@layer components {
  .page-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6;
  }

  .page-title {
    @apply text-2xl md:text-3xl font-bold text-gray-900 mb-2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .page-subtitle {
    @apply text-base text-gray-600 mb-6;
  }

  .section-title {
    @apply text-xl font-semibold text-gray-900 mb-4;
  }

  /* Enhanced 3D Card Effects */
  .card-3d {
    @apply bg-white/80 backdrop-blur-sm border border-white/20 rounded-xl shadow-lg;
    transform: translateZ(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .card-3d:hover {
    transform: translateY(-4px) translateZ(0);
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-card-hover;
  }

  /* Glassmorphism Effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-dark {
    @apply bg-gray-900/10 backdrop-blur-md border border-gray-700/20;
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }

  /* Enhanced Input Focus */
  .input-focus {
    @apply focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  /* 3D Button Effects */
  .btn-3d {
    @apply relative overflow-hidden;
    transform: translateZ(0);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-3d:before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    transform: translateX(-100%);
    transition: transform 0.6s;
  }

  .btn-3d:hover:before {
    transform: translateX(100%);
  }

  .btn-3d:hover {
    transform: translateY(-1px) translateZ(0);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }

  .btn-3d:active {
    transform: translateY(0) translateZ(0);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  }

  /* Floating Animation */
  .float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes shrink {
    0% {
      width: 100%;
    }
    100% {
      width: 0%;
    }
  }

  /* Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 via-purple-600 to-secondary-600 bg-clip-text text-transparent;
  }

  /* Enhanced Shadows */
  .shadow-3d {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 0 0 1px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .shadow-3d-hover {
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* Animated Background */
  .animated-bg {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Neon Glow Effect */
  .neon-glow {
    box-shadow:
      0 0 5px rgba(99, 102, 241, 0.5),
      0 0 10px rgba(99, 102, 241, 0.3),
      0 0 15px rgba(99, 102, 241, 0.2),
      0 0 20px rgba(99, 102, 241, 0.1);
  }

  /* Morphism Card */
  .morphism {
    @apply bg-white/20 backdrop-blur-lg border border-white/30 rounded-2xl;
    box-shadow:
      0 8px 32px 0 rgba(31, 38, 135, 0.37),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}
