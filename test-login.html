<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login - Gudang <PERSON>tra</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .demo-btn {
            background: #28a745;
        }
        .demo-btn:hover {
            background: #1e7e34;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .credentials {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .credentials h3 {
            margin-top: 0;
        }
        .cred-item {
            margin: 5px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Login - Gudang Mitra</h1>
        
        <div class="credentials">
            <h3>📋 Available Test Credentials:</h3>
            <div class="cred-item"><strong>Manager:</strong> <EMAIL> / password123</div>
            <div class="cred-item"><strong>User:</strong> <EMAIL> / password</div>
            <div class="cred-item"><strong>Admin:</strong> <EMAIL> / password123</div>
            <div class="cred-item"><strong>Admin:</strong> <EMAIL> / password123</div>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit">🚀 Login</button>
            <button type="button" class="demo-btn" onclick="useManager()">👤 Use Manager</button>
            <button type="button" class="demo-btn" onclick="useUser()">👤 Use User</button>
            <button type="button" class="demo-btn" onclick="testConnection()">🔗 Test Connection</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:3002/api';
        
        function useManager() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'password123';
        }
        
        function useUser() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'password';
        }
        
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '🔄 Testing connection...';
            
            try {
                const response = await fetch(`${API_URL}/test`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ Connection Test Successful!\n\nResponse: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Connection Test Failed!\n\nError: ${error.message}\n\nMake sure the backend server is running on http://localhost:3002`;
            }
        }
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '🔄 Logging in...';
            
            try {
                console.log('Attempting login with:', { email, password });
                
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                console.log('Response status:', response.status);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `🎉 Login Successful!\n\nUser Data:\n${JSON.stringify(data.user, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Login Failed!\n\nError: ${data.message || 'Unknown error'}`;
                }
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Login Failed!\n\nError: ${error.message}\n\nMake sure the backend server is running on http://localhost:3002`;
            }
        });
        
        // Test connection on page load
        window.addEventListener('load', () => {
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
