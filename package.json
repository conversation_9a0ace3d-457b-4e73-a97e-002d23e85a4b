{"name": "item-request-management-system", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "node server/railway-server.js", "server": "node server/railway-server.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "lucide-react": "^0.344.0", "mysql2": "^3.14.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.21.3", "sonner": "^2.0.3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}